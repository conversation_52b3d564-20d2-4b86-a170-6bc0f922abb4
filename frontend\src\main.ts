import { createApp } from 'vue'
import { createPinia } from 'pinia'
import './style.css'
import App from './App.vue'

// 开发环境下运行安全测试
if (import.meta.env.DEV) {
  import('./utils/security-test').then(({ testXSSProtection, testNormalFormatting }) => {
    console.log('🔒 运行安全测试...')
    testXSSProtection()
    testNormalFormatting()
  })
}

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.mount('#app')
