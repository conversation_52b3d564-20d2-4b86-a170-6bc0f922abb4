"""
聊天消息数据模型
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

class MessageType(str, Enum):
    """消息类型枚举"""
    USER = "user"           # 用户消息
    ASSISTANT = "assistant" # AI助手消息
    SYSTEM = "system"       # 系统消息
    ERROR = "error"         # 错误消息

class ChatMessage(BaseModel):
    """聊天消息模型"""
    id: Optional[str] = Field(None, description="消息ID")
    type: MessageType = Field(MessageType.USER, description="消息类型")
    content: str = Field(..., description="消息内容")
    user_id: str = Field(..., description="用户ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")

    model_config = {
        "json_encoders": {
            datetime: lambda v: v.isoformat()
        }
    }

class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    action: str = Field(..., description="操作类型")
    data: Dict[str, Any] = Field(..., description="消息数据")
    client_id: str = Field(..., description="客户端ID")

class WebSocketResponse(BaseModel):
    """统一的WebSocket响应模型"""
    action: str = Field(..., description="响应动作类型")
    type: str = Field(..., description="响应类型: success, error, message, system")
    data: Dict[str, Any] = Field(default_factory=dict, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    client_id: Optional[str] = Field(None, description="目标客户端ID")

    model_config = {
        "json_encoders": {
            datetime: lambda v: v.isoformat()
        }
    }

class LLMRequest(BaseModel):
    """大模型请求模型"""
    message: str = Field(..., description="用户消息")
    conversation_id: Optional[str] = Field(None, description="会话ID")
    model: str = Field("gpt-3.5-turbo", description="模型名称")
    temperature: float = Field(0.7, description="温度参数")
    max_tokens: int = Field(1000, description="最大token数")
    stream: bool = Field(True, description="是否流式响应")

class LLMResponse(BaseModel):
    """大模型响应模型"""
    content: str = Field(..., description="响应内容")
    model: str = Field(..., description="使用的模型")
    usage: Optional[Dict[str, int]] = Field(None, description="token使用情况")
    finish_reason: Optional[str] = Field(None, description="完成原因")
