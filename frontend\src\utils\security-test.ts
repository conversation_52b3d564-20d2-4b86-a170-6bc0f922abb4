/**
 * XSS安全测试工具
 * 用于验证消息内容的安全处理
 */

import DOMPurify from 'dompurify'

// 常见的XSS攻击载荷
export const XSS_TEST_PAYLOADS = [
  '<script>alert("XSS")</script>',
  '<img src="x" onerror="alert(\'XSS\')">',
  '<svg onload="alert(\'XSS\')">',
  'javascript:alert("XSS")',
  '<iframe src="javascript:alert(\'XSS\')"></iframe>',
  '<div onclick="alert(\'XSS\')">Click me</div>',
  '<style>body{background:url("javascript:alert(\'XSS\')")}</style>',
  '<link rel="stylesheet" href="javascript:alert(\'XSS\')">',
  '<meta http-equiv="refresh" content="0;url=javascript:alert(\'XSS\')">',
  '<form><button formaction="javascript:alert(\'XSS\')">Submit</button></form>'
]

// 安全的消息格式化函数（与MessageList.vue中的保持一致）
export function formatContentSafely(content: string): string {
  // 先进行基本的文本格式化
  const formatted = content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
  
  // 使用DOMPurify清理HTML，防止XSS攻击
  return DOMPurify.sanitize(formatted, {
    ALLOWED_TAGS: ['br', 'strong', 'em', 'code', 'p', 'div', 'span'],
    ALLOWED_ATTR: ['class']
  })
}

// 测试XSS防护效果
export function testXSSProtection(): void {
  console.log('=== XSS防护测试开始 ===')
  
  XSS_TEST_PAYLOADS.forEach((payload, index) => {
    const sanitized = formatContentSafely(payload)
    const isBlocked = !sanitized.includes('<script') && 
                     !sanitized.includes('javascript:') && 
                     !sanitized.includes('onerror=') &&
                     !sanitized.includes('onload=') &&
                     !sanitized.includes('onclick=')
    
    console.log(`测试 ${index + 1}:`, {
      原始载荷: payload,
      清理后: sanitized,
      是否安全: isBlocked ? '✅ 已阻止' : '❌ 可能存在风险'
    })
  })
  
  console.log('=== XSS防护测试完成 ===')
}

// 测试正常格式化功能
export function testNormalFormatting(): void {
  console.log('=== 正常格式化测试开始 ===')
  
  const testCases = [
    '这是**粗体**文本',
    '这是*斜体*文本',
    '这是`代码`文本',
    '这是多行\n文本\n内容',
    '混合格式：**粗体** 和 *斜体* 以及 `代码`'
  ]
  
  testCases.forEach((testCase, index) => {
    const formatted = formatContentSafely(testCase)
    console.log(`格式化测试 ${index + 1}:`, {
      原始: testCase,
      格式化后: formatted
    })
  })
  
  console.log('=== 正常格式化测试完成 ===')
}
