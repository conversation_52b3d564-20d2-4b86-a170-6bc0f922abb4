/**
 * 聊天状态管理 - Pinia Store
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { WebSocketService, type ChatMessage } from '../services/websocket'

export interface ChatSession {
  id: string
  title: string
  created_at: string
  updated_at: string
  last_message?: {
    content: string
    timestamp: string
  }
}

export interface ChatState {
  messages: ChatMessage[]
  sessions: ChatSession[]
  currentSessionId: string | null
  isConnected: boolean
  isConnecting: boolean
  connectionStatus: string
  currentUser: string
  isTyping: boolean
  error: string | null
  isLoadingHistory: boolean
}

export const useChatStore = defineStore('chat', () => {
  // 状态
  const messages = ref<ChatMessage[]>([])
  const sessions = ref<ChatSession[]>([])
  const currentSessionId = ref<string | null>(null)
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const connectionStatus = ref('未连接')
  const currentUser = ref('')
  const isTyping = ref(false)
  const error = ref<string | null>(null)
  const isLoadingHistory = ref(false)
  
  // WebSocket服务实例
  let wsService: WebSocketService | null = null

  // 计算属性
  const messageCount = computed(() => messages.value.length)
  const lastMessage = computed(() => 
    messages.value.length > 0 ? messages.value[messages.value.length - 1] : null
  )
  const userMessages = computed(() => 
    messages.value.filter(msg => msg.type === 'user')
  )
  const assistantMessages = computed(() => 
    messages.value.filter(msg => msg.type === 'assistant')
  )

  // 生成客户端ID
  const generateClientId = (): string => {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 初始化WebSocket连接
  const initializeConnection = async (username?: string): Promise<void> => {
    if (wsService) {
      return
    }

    const clientId = username || generateClientId()
    currentUser.value = clientId

    // 确定WebSocket URL
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.hostname
    const port = '8000' // 后端WebSocket服务端口
    const wsUrl = `${protocol}//${host}:${port}`

    console.log('WebSocket URL:', wsUrl)

    wsService = new WebSocketService(wsUrl, clientId)

    // 设置事件监听器
    setupEventListeners()

    // 连接WebSocket
    isConnecting.value = true
    try {
      await wsService.connect()

      // 等待连接状态同步
      let retries = 0
      while (!isConnected.value && retries < 50) {
        await new Promise(resolve => setTimeout(resolve, 100))
        retries++
      }

      if (!isConnected.value) {
        throw new Error('连接状态同步超时')
      }

      console.log('WebSocket连接完全建立')
    } catch (err) {
      error.value = `连接失败: ${err}`
      isConnecting.value = false
      throw err
    }
  }

  // 设置事件监听器
  const setupEventListeners = (): void => {
    if (!wsService) return

    // 连接成功
    wsService.on('connected', () => {
      isConnected.value = true
      isConnecting.value = false
      connectionStatus.value = '已连接'
      error.value = null
      console.log('WebSocket连接成功')
    })

    // 连接断开
    wsService.on('disconnected', (data: any) => {
      isConnected.value = false
      isConnecting.value = false
      connectionStatus.value = data.wasClean ? '连接已断开' : '连接异常断开'
      console.log('WebSocket连接断开:', data.reason || '未知原因')
    })

    // 聊天响应 - 更新为统一格式
    wsService.on('chat_response', (response: any) => {
      isTyping.value = false

      if (response.type === 'error') {
        error.value = response.data.message || '聊天处理失败'
        return
      }

      const data = response.data
      // 验证响应的会话ID是否与当前会话匹配
      if (data.session_id && currentSessionId.value && data.session_id !== currentSessionId.value) {
        console.warn('收到的聊天响应会话ID与当前会话不匹配，忽略此响应')
        return
      }

      // 只添加AI回复，用户消息已在sendMessage时添加
      if (data.ai_message) {
        // 确保AI消息包含会话信息
        if (!data.ai_message.metadata) {
          data.ai_message.metadata = {}
        }
        data.ai_message.metadata.session_id = data.session_id || currentSessionId.value

        addMessage(data.ai_message)
      }
    })

    // 错误处理 - 更新为统一格式
    wsService.on('error', (response: any) => {
      const errorMessage = response.data?.message || response.error || '发生未知错误'
      error.value = errorMessage
      isTyping.value = false

      addMessage({
        type: 'error',
        content: `错误: ${errorMessage}`,
        user_id: 'system',
        timestamp: new Date().toISOString()
      })
    })

    // 心跳响应
    wsService.on('pong', () => {
      connectionStatus.value = '连接正常'
    })

    // 历史消息加载完成 - 更新为统一格式
    wsService.on('history_loaded', (response: any) => {
      handleHistoryLoaded(response.data || response)
    })

    // 会话列表加载完成 - 更新为统一格式
    wsService.on('sessions_loaded', (response: any) => {
      handleSessionsLoaded(response.data || response)
    })

    // 新会话创建完成 - 更新为统一格式
    wsService.on('session_created', (response: any) => {
      handleSessionCreated(response.data || response)
    })
  }

  // 发送消息
  const sendMessage = async (content: string): Promise<void> => {
    if (!wsService || !isConnected.value) {
      error.value = '未连接到服务器'
      return
    }

    if (!content.trim()) {
      error.value = '消息内容不能为空'
      return
    }

    try {
      // 清除之前的错误
      error.value = null

      // 立即添加用户消息到列表
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'user',
        content: content.trim(),
        timestamp: new Date().toISOString(),
        user_id: currentUser.value || 'anonymous',
        metadata: {
          session_id: currentSessionId.value
        }
      }
      addMessage(userMessage)

      // 设置AI正在输入状态
      isTyping.value = true

      // 发送消息到服务器，包含当前会话ID
      wsService.sendMessage(content.trim(), currentSessionId.value)

    } catch (err) {
      error.value = `发送消息失败: ${err}`
      isTyping.value = false
    }
  }

  // 添加消息到列表
  const addMessage = (message: ChatMessage): void => {
    // 生成消息ID（如果没有）
    if (!message.id) {
      message.id = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    // 如果消息包含会话ID，验证是否与当前会话匹配
    if (message.metadata?.session_id && currentSessionId.value) {
      if (message.metadata.session_id !== currentSessionId.value) {
        console.warn('消息的会话ID与当前会话不匹配，忽略此消息', {
          messageSessionId: message.metadata.session_id,
          currentSessionId: currentSessionId.value
        })
        return
      }
    }

    // 检查是否已存在相同内容的消息（防止重复）
    const isDuplicate = messages.value.some(existingMessage =>
      existingMessage.content === message.content &&
      existingMessage.type === message.type &&
      Math.abs(new Date(existingMessage.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000 // 5秒内的相同消息视为重复
    )

    if (!isDuplicate) {
      messages.value.push(message)
    }
  }

  // 清空消息
  const clearMessages = (): void => {
    messages.value = []
  }

  // 断开连接
  const disconnect = (): void => {
    if (wsService) {
      wsService.disconnect()
      wsService = null
    }
    
    isConnected.value = false
    isConnecting.value = false
    connectionStatus.value = '未连接'
    currentUser.value = ''
  }

  // 重新连接
  const reconnect = async (): Promise<void> => {
    disconnect()
    await initializeConnection(currentUser.value)
  }

  // 清除错误
  const clearError = (): void => {
    error.value = null
  }

  // 设置用户名
  const setUsername = (username: string): void => {
    currentUser.value = username
  }

  // 加载历史消息
  const loadHistory = async (sessionId?: string, limit: number = 50, offset: number = 0): Promise<void> => {
    if (!wsService) {
      throw new Error('WebSocket服务未初始化')
    }

    isLoadingHistory.value = true
    try {
      wsService.loadHistory(sessionId, limit, offset)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载历史消息失败'
      throw err
    }
  }

  // 获取会话列表
  const getSessions = async (): Promise<void> => {
    if (!wsService) {
      throw new Error('WebSocket服务未初始化')
    }

    try {
      wsService.getSessions()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取会话列表失败'
      throw err
    }
  }

  // 创建新会话
  const createSession = async (title: string = '新会话'): Promise<void> => {
    if (!wsService) {
      throw new Error('WebSocket服务未初始化')
    }

    try {
      wsService.createSession(title)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建会话失败'
      throw err
    }
  }

  // 切换会话
  const switchSession = async (sessionId: string): Promise<void> => {
    try {
      // 先设置新的会话ID
      currentSessionId.value = sessionId

      // 清空当前消息，准备加载新会话的消息
      messages.value = []

      // 清除错误状态
      error.value = null

      // 加载新会话的历史消息
      await loadHistory(sessionId)

      console.log(`已切换到会话: ${sessionId}`)
    } catch (err) {
      console.error('切换会话失败:', err)
      error.value = '切换会话失败'
    }
  }

  // 处理历史消息加载完成
  const handleHistoryLoaded = (data: any): void => {
    isLoadingHistory.value = false
    if (data.messages && Array.isArray(data.messages)) {
      // 验证返回的会话ID是否与当前会话ID匹配
      if (data.session_id && currentSessionId.value && data.session_id !== currentSessionId.value) {
        console.warn('历史消息的会话ID与当前会话ID不匹配，忽略此次加载')
        return
      }

      // 将历史消息转换为ChatMessage格式
      const historyMessages: ChatMessage[] = data.messages.map((msg: any) => ({
        id: msg.id,
        type: msg.type,
        content: msg.content,
        user_id: msg.user_id,
        timestamp: msg.timestamp,
        metadata: msg.metadata
      }))

      // 设置消息列表（历史消息按时间顺序排列）
      messages.value = historyMessages

      // 只有在当前没有会话ID时才设置
      if (!currentSessionId.value && data.session_id) {
        currentSessionId.value = data.session_id
      }
    }
  }

  // 处理会话列表加载完成
  const handleSessionsLoaded = (data: any): void => {
    if (data.sessions && Array.isArray(data.sessions)) {
      sessions.value = data.sessions
    }
  }

  // 处理新会话创建完成
  const handleSessionCreated = (data: any): void => {
    if (data.session_id) {
      currentSessionId.value = data.session_id
      // 添加新会话到会话列表
      const newSession: ChatSession = {
        id: data.session_id,
        title: data.title || '新会话',
        created_at: data.timestamp,
        updated_at: data.timestamp
      }
      sessions.value.unshift(newSession)
      // 清空当前消息
      messages.value = []
    }
  }

  // 获取连接状态文本
  const getConnectionStatusText = (): string => {
    if (isConnecting.value) return '连接中...'
    if (isConnected.value) return '已连接'
    return '未连接'
  }

  return {
    // 状态
    messages,
    sessions,
    currentSessionId,
    isConnected,
    isConnecting,
    connectionStatus,
    currentUser,
    isTyping,
    error,
    isLoadingHistory,

    // 计算属性
    messageCount,
    lastMessage,
    userMessages,
    assistantMessages,

    // 方法
    initializeConnection,
    sendMessage,
    addMessage,
    clearMessages,
    disconnect,
    reconnect,
    clearError,
    setUsername,
    loadHistory,
    getSessions,
    createSession,
    switchSession,
    handleHistoryLoaded,
    handleSessionsLoaded,
    handleSessionCreated,
    getConnectionStatusText
  }
})
