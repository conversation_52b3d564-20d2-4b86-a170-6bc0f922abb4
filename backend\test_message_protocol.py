#!/usr/bin/env python3
"""
WebSocket消息协议测试脚本
用于验证统一消息格式的正确性
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.message import WebSocketResponse, MessageType
from app.websocket.handlers import MessageHandler

async def test_unified_response_format():
    """测试统一响应格式"""
    print("=== 测试统一WebSocket响应格式 ===")
    
    handler = MessageHandler()
    
    # 测试成功响应
    success_response = await handler._create_unified_response(
        action="test_action",
        response_type="success",
        data={"message": "测试成功", "value": 123},
        client_id="test_client"
    )
    
    print("成功响应格式:")
    print(json.dumps(json.loads(success_response), indent=2, ensure_ascii=False))
    
    # 测试错误响应
    error_response = await handler._create_error_response(
        error_message="测试错误消息",
        error_code="TEST_ERROR"
    )
    
    print("\n错误响应格式:")
    print(json.dumps(json.loads(error_response), indent=2, ensure_ascii=False))
    
    # 验证响应格式的一致性
    success_data = json.loads(success_response)
    error_data = json.loads(error_response)
    
    required_fields = ['action', 'type', 'data', 'timestamp']
    
    print("\n格式验证:")
    for field in required_fields:
        success_has = field in success_data
        error_has = field in error_data
        print(f"  {field}: 成功响应{'✅' if success_has else '❌'} | 错误响应{'✅' if error_has else '❌'}")
    
    print("=== 测试完成 ===\n")

async def test_message_handling():
    """测试消息处理"""
    print("=== 测试消息处理流程 ===")
    
    handler = MessageHandler()
    client_id = "test_client_123"
    
    # 测试聊天消息
    chat_message = {
        "action": "chat",
        "content": "你好，这是一条测试消息",
        "session_id": None
    }
    
    print("发送聊天消息:")
    print(json.dumps(chat_message, indent=2, ensure_ascii=False))
    
    try:
        response = await handler.handle_message(
            json.dumps(chat_message, ensure_ascii=False),
            client_id
        )
        
        print("\n收到响应:")
        response_data = json.loads(response)
        print(json.dumps(response_data, indent=2, ensure_ascii=False))
        
        # 验证响应结构
        if response_data.get('type') == 'success':
            print("✅ 消息处理成功")
        else:
            print("❌ 消息处理失败")
            
    except Exception as e:
        print(f"❌ 消息处理异常: {e}")
    
    # 测试ping消息
    print("\n测试ping消息:")
    ping_response = await handler._handle_ping_message(client_id)
    ping_data = json.loads(ping_response)
    print(json.dumps(ping_data, indent=2, ensure_ascii=False))
    
    print("=== 测试完成 ===\n")

async def test_error_handling():
    """测试错误处理"""
    print("=== 测试错误处理 ===")
    
    handler = MessageHandler()
    client_id = "test_client_456"
    
    # 测试空消息
    empty_message = {
        "action": "chat",
        "content": "",
        "session_id": None
    }
    
    print("发送空消息:")
    print(json.dumps(empty_message, indent=2, ensure_ascii=False))
    
    try:
        response = await handler.handle_message(
            json.dumps(empty_message, ensure_ascii=False),
            client_id
        )
        
        print("\n收到错误响应:")
        response_data = json.loads(response)
        print(json.dumps(response_data, indent=2, ensure_ascii=False))
        
        if response_data.get('type') == 'error':
            print("✅ 错误处理正确")
        else:
            print("❌ 错误处理异常")
            
    except Exception as e:
        print(f"❌ 错误处理异常: {e}")
    
    # 测试未知动作
    print("\n测试未知动作:")
    unknown_message = {
        "action": "unknown_action",
        "data": "test"
    }
    
    try:
        response = await handler.handle_message(
            json.dumps(unknown_message, ensure_ascii=False),
            client_id
        )
        
        response_data = json.loads(response)
        print(json.dumps(response_data, indent=2, ensure_ascii=False))
        
        if response_data.get('type') == 'error':
            print("✅ 未知动作错误处理正确")
        else:
            print("❌ 未知动作错误处理异常")
            
    except Exception as e:
        print(f"❌ 未知动作处理异常: {e}")
    
    print("=== 测试完成 ===\n")

async def main():
    """主测试函数"""
    print("开始WebSocket消息协议测试...\n")
    
    await test_unified_response_format()
    await test_message_handling()
    await test_error_handling()
    
    print("所有测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
