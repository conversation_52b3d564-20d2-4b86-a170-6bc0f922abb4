/**
 * WebSocket服务类
 * 负责管理与后端的WebSocket连接
 */

export interface ChatMessage {
  id?: string
  type: 'user' | 'assistant' | 'system' | 'error'
  content: string
  user_id: string
  timestamp: string
  metadata?: Record<string, any>
}

export interface WebSocketMessage {
  action: string
  content?: string
  data?: any
  client_id?: string
  timestamp?: string
  session_id?: string
  limit?: number
  offset?: number
  title?: string
}

export interface WebSocketResponse {
  action: string
  type: 'success' | 'error' | 'message' | 'system'
  data: Record<string, any>
  timestamp: string
  client_id?: string
}

export class WebSocketService {
  private socket: WebSocket | null = null
  private url: string
  private clientId: string
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 1000
  private heartbeatInterval: number | null = null
  private messageHandlers: Map<string, Function[]> = new Map()
  private isConnecting = false

  constructor(url: string, clientId: string) {
    this.url = url
    this.clientId = clientId
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.isConnecting || this.isConnected()) {
      return
    }

    this.isConnecting = true

    return new Promise((resolve, reject) => {
      try {
        const wsUrl = `${this.url}/ws/${this.clientId}`
        console.log(`正在连接WebSocket: ${wsUrl}`)
        console.log(`客户端ID: ${this.clientId}`)

        this.socket = new WebSocket(wsUrl)

        // 连接成功处理
        this.socket.onopen = (event) => {
          this.handleOpen(event)
          resolve() // 连接成功时resolve Promise
        }

        // 连接错误处理
        this.socket.onerror = (event) => {
          this.handleError(event)
          reject(new Error('WebSocket连接失败'))
        }

        this.socket.onmessage = this.handleMessage.bind(this)
        this.socket.onclose = this.handleClose.bind(this)

        // 添加连接超时检测
        setTimeout(() => {
          if (this.isConnecting && this.socket && this.socket.readyState !== WebSocket.OPEN) {
            console.error('WebSocket连接超时')
            this.socket.close()
            this.isConnecting = false
            reject(new Error('WebSocket连接超时'))
          }
        }, 10000) // 10秒超时

      } catch (error) {
        console.error('WebSocket连接失败:', error)
        this.isConnecting = false
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    if (this.socket) {
      this.socket.close(1000, '客户端主动断开连接')
      this.socket = null
    }

    this.reconnectAttempts = 0
    this.isConnecting = false
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN
  }

  /**
   * 发送消息
   */
  sendMessage(message: string, sessionId?: string): void {
    if (!this.isConnected()) {
      console.warn('WebSocket未连接，无法发送消息')
      return
    }

    const messageData: WebSocketMessage = {
      action: 'chat',
      content: message,
      session_id: sessionId,
      client_id: this.clientId,
      timestamp: new Date().toISOString()
    }

    this.socket!.send(JSON.stringify(messageData))
  }

  /**
   * 加载历史消息
   */
  loadHistory(sessionId?: string, limit: number = 50, offset: number = 0): void {
    if (!this.isConnected()) {
      console.warn('WebSocket未连接，无法加载历史消息')
      return
    }

    const messageData: WebSocketMessage = {
      action: 'load_history',
      session_id: sessionId,
      limit: limit,
      offset: offset,
      client_id: this.clientId,
      timestamp: new Date().toISOString()
    }

    this.socket!.send(JSON.stringify(messageData))
  }

  /**
   * 获取会话列表
   */
  getSessions(): void {
    if (!this.isConnected()) {
      console.warn('WebSocket未连接，无法获取会话列表')
      return
    }

    const messageData: WebSocketMessage = {
      action: 'get_sessions',
      client_id: this.clientId,
      timestamp: new Date().toISOString()
    }

    this.socket!.send(JSON.stringify(messageData))
  }

  /**
   * 创建新会话
   */
  createSession(title: string = '新会话'): void {
    if (!this.isConnected()) {
      console.warn('WebSocket未连接，无法创建会话')
      return
    }

    const messageData: WebSocketMessage = {
      action: 'create_session',
      title: title,
      client_id: this.clientId,
      timestamp: new Date().toISOString()
    }

    this.socket!.send(JSON.stringify(messageData))
  }

  /**
   * 发送JSON数据
   */
  sendJSON(data: WebSocketMessage): void {
    if (!this.isConnected()) {
      console.warn('WebSocket未连接，无法发送数据')
      return
    }

    this.socket!.send(JSON.stringify(data))
  }

  /**
   * 发送心跳
   */
  private sendHeartbeat(): void {
    if (this.isConnected()) {
      this.sendJSON({
        action: 'ping',
        client_id: this.clientId,
        timestamp: new Date().toISOString()
      })
    }
  }

  /**
   * 添加消息处理器
   */
  on(event: string, handler: Function): void {
    if (!this.messageHandlers.has(event)) {
      this.messageHandlers.set(event, [])
    }
    this.messageHandlers.get(event)!.push(handler)
  }

  /**
   * 移除消息处理器
   */
  off(event: string, handler: Function): void {
    const handlers = this.messageHandlers.get(event)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data: any): void {
    const handlers = this.messageHandlers.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`事件处理器执行失败 [${event}]:`, error)
        }
      })
    }
  }

  /**
   * 处理连接打开
   */
  private handleOpen(event: Event): void {
    console.log('WebSocket连接已建立')
    this.isConnecting = false
    this.reconnectAttempts = 0

    // 启动心跳
    this.heartbeatInterval = window.setInterval(() => {
      this.sendHeartbeat()
    }, 30000) // 30秒心跳

    this.emit('connected', { clientId: this.clientId })
  }

  /**
   * 处理接收消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const response: WebSocketResponse = JSON.parse(event.data)
      console.log('收到WebSocket消息:', response)

      // 统一处理响应格式
      if (response.type === 'error') {
        console.error('WebSocket错误:', response.data)
        this.emit('error', response)
        return
      }

      // 处理不同类型的消息
      switch (response.action) {
        case 'chat_response':
          this.emit('chat_response', response)
          break
        case 'history_loaded':
          this.emit('history_loaded', response)
          break
        case 'sessions_loaded':
          this.emit('sessions_loaded', response)
          break
        case 'session_created':
          this.emit('session_created', response)
          break
        case 'pong':
          this.emit('pong', response)
          break
        case 'system_response':
          this.emit('system', response)
          break
        default:
          this.emit('message', response)
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
      this.emit('error', { error: '消息解析失败', raw: event.data })
    }
  }

  /**
   * 处理连接关闭
   */
  private handleClose(event: CloseEvent): void {
    console.log(`WebSocket连接已关闭: ${event.code} - ${event.reason}`)
    this.isConnecting = false

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    this.emit('disconnected', { 
      code: event.code, 
      reason: event.reason,
      wasClean: event.wasClean 
    })

    // 自动重连（除非是正常关闭）
    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect()
    }
  }

  /**
   * 处理连接错误
   */
  private handleError(event: Event): void {
    console.error('WebSocket连接错误:', event)
    this.isConnecting = false
    this.emit('error', { error: 'WebSocket连接错误', event })
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连...`)
    
    setTimeout(() => {
      if (this.reconnectAttempts <= this.maxReconnectAttempts) {
        this.connect().catch(error => {
          console.error('重连失败:', error)
        })
      }
    }, delay)
  }

  /**
   * 获取连接状态
   */
  getReadyState(): number {
    return this.socket?.readyState ?? WebSocket.CLOSED
  }

  /**
   * 获取连接状态描述
   */
  getReadyStateText(): string {
    const state = this.getReadyState()
    switch (state) {
      case WebSocket.CONNECTING: return '连接中'
      case WebSocket.OPEN: return '已连接'
      case WebSocket.CLOSING: return '关闭中'
      case WebSocket.CLOSED: return '已关闭'
      default: return '未知状态'
    }
  }
}
