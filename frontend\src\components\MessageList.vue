<template>
  <div class="message-list">
    <div
      v-for="message in messages"
      :key="message.id"
      class="message-wrapper"
      :class="getMessageClass(message)"
    >
      <div class="message-bubble">
        <div class="message-header" v-if="showMessageHeader(message)">
          <span class="message-user">{{ getMessageUser(message) }}</span>
          <span class="message-time">{{ formatTime(message.timestamp) }}</span>
        </div>
        <div class="message-content" v-html="formatContent(message.content)"></div>
      </div>
    </div>
    
    <!-- 打字指示器 -->
    <div v-if="isTyping" class="message-wrapper message-assistant">
      <div class="message-bubble typing-indicator">
        <div class="typing-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <span class="typing-text">AI正在思考中...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import DOMPurify from 'dompurify'
import type { ChatMessage } from '../services/websocket'

interface Props {
  messages: ChatMessage[]
  isTyping?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isTyping: false
})

// 获取消息样式类
const getMessageClass = (message: ChatMessage) => {
  return {
    'message-user': message.type === 'user',
    'message-assistant': message.type === 'assistant',
    'message-system': message.type === 'system',
    'message-error': message.type === 'error'
  }
}

// 是否显示消息头部
const showMessageHeader = (message: ChatMessage) => {
  return message.type !== 'system'
}

// 获取消息用户名
const getMessageUser = (message: ChatMessage) => {
  switch (message.type) {
    case 'user':
      return '我'
    case 'assistant':
      return 'AI助手'
    case 'system':
      return '系统'
    case 'error':
      return '错误'
    default:
      return message.user_id
  }
}

// 格式化时间
const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  // 如果是今天
  if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }
  
  // 如果是昨天
  const yesterday = new Date(now)
  yesterday.setDate(yesterday.getDate() - 1)
  if (date.getDate() === yesterday.getDate()) {
    return `昨天 ${date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })}`
  }
  
  // 其他日期
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化消息内容 - 修复XSS漏洞
const formatContent = (content: string) => {
  // 先进行基本的文本格式化
  const formatted = content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')

  // 使用DOMPurify清理HTML，防止XSS攻击
  return DOMPurify.sanitize(formatted, {
    ALLOWED_TAGS: ['br', 'strong', 'em', 'code', 'p', 'div', 'span'],
    ALLOWED_ATTR: ['class']
  })
}
</script>

<style scoped>
.message-list {
  display: flex;
  flex-direction: column;
  gap: 12px; /* 减少消息间距 */
  width: 100%; /* 确保铺满整个宽度 */
  padding: 16px; /* 添加内边距 */
  padding-bottom: 60px; /* 大幅增加底部padding，确保消息完全可见 */
  margin: 0; /* 确保没有margin */
  box-sizing: border-box; /* 确保padding计算在内 */
  min-height: 100%; /* 确保最小高度 */
}

.message-wrapper {
  display: flex;
  max-width: 85%;
  margin: 0;
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}

.message-wrapper.message-user {
  max-width: 100%;
  justify-content: flex-end;
}

.message-user {
  align-self: flex-end;
  margin-left: auto;
  margin-right: 0;
  justify-content: flex-end;
  padding-right: 0;
}

.message-assistant,
.message-system,
.message-error {
  align-self: flex-start;
  margin-right: auto;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  max-width: min(320px, 80%); /* 增加最大宽度，更好利用空间 */
  min-width: 40px; /* 减少最小宽度，让短文本更紧凑 */
  width: fit-content; /* 根据内容自适应宽度 */
  line-height: 1.5; /* 改善文本行高 */
  display: inline-block; /* 确保宽度自适应 */
}

.message-user .message-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 4px;
  margin-left: auto;
  margin-right: 0;
}

.message-assistant .message-bubble {
  background: #ffffff;
  color: #1f2937;
  border: 1px solid #e5e7eb;
  border-bottom-left-radius: 4px;
}

.message-system .message-bubble {
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
  font-size: 14px;
  text-align: center;
  border-radius: 12px;
}

.message-error .message-bubble {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
  border-bottom-left-radius: 4px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  opacity: 0.8;
}

.message-user .message-header {
  color: rgba(255, 255, 255, 0.9);
}

.message-assistant .message-header,
.message-error .message-header {
  color: #6b7280;
}

.message-user {
  font-weight: 500;
}

.message-time {
  font-size: 11px;
  margin-left: 8px;
}

.message-content {
  line-height: 1.5;
  font-size: 14px;
}

.message-content :deep(strong) {
  font-weight: 600;
}

.message-content :deep(em) {
  font-style: italic;
}

.message-content :deep(code) {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.message-user .message-content :deep(code) {
  background: rgba(255, 255, 255, 0.2);
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  color: #6b7280 !important;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #9ca3af;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-text {
  font-size: 13px;
  color: #9ca3af;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
/* 平板端适配 */
@media (max-width: 768px) {
  .message-wrapper {
    max-width: 90%; /* 增加宽度利用 */
    padding: 0 6px; /* 使用padding代替margin */
  }

  .message-bubble {
    padding: 10px 14px;
    font-size: 14px;
    max-width: min(280px, 85%); /* 增加平板端宽度 */
  }

  .message-header {
    font-size: 11px;
  }
}

/* 手机端适配 */
@media (max-width: 480px) {
  .message-wrapper {
    max-width: 95%; /* 手机端最大化宽度利用 */
    padding: 0 4px; /* 使用padding代替margin */
  }

  .message-bubble {
    padding: 8px 12px;
    font-size: 14px;
    max-width: min(260px, 90%); /* 手机端增加宽度 */
    border-radius: 16px;
  }

  .message-header {
    font-size: 10px;
    margin-bottom: 4px;
  }

  .message-content {
    line-height: 1.4;
  }
}

/* 小屏手机适配 */
@media (max-width: 360px) {
  .message-wrapper {
    max-width: 98%; /* 小屏最大化利用空间 */
    padding: 0 2px; /* 使用最小padding */
  }

  .message-bubble {
    max-width: min(220px, 95%); /* 小屏手机增加宽度 */
    padding: 6px 10px;
  }
}
</style>
